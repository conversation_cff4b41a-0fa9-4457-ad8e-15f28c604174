from fastapi import APIRouter, Depends, HTTPException
from models.models import AuthData
from database.dependencies import get_db
from services.test_service import create_user

router = APIRouter(prefix="/auth", tags=["Auth"])

@router.post("/")
async def add_user(data: AuthData, db=Depends(get_db)):
    user_id = await create_user(db, data)
    return {"message": "User added", "user_id": user_id}



