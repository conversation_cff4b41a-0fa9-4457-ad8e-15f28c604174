from fastapi import FastAPI
import uvicorn

from database.database import client
# from middleware.auth_middleware import MyMiddleware

from routes.test_route import router as auth_router

app = FastAPI(title="BY Pilates API", version="1.0.0", description="API for BY Pilates application")

# Custom middleware
# app.add_middleware(MyMiddleware)

# Routers
app.include_router(auth_router)

@app.on_event("startup")
async def startup_event():
    print("MongoDB connected:", client is not None)

@app.on_event("shutdown")
async def shutdown_event():
    client.close()
    print("MongoDB connection closed")

if __name__ == "__main__":
    uvicorn.run(app, host="localhost", port=8000)
